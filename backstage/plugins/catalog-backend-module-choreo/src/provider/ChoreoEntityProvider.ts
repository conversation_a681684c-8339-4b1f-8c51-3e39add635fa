import {
  EntityProvider,
  EntityProviderConnection,
} from '@backstage/plugin-catalog-node';
import { Entity } from '@backstage/catalog-model';
import { SchedulerServiceTaskRunner } from '@backstage/backend-plugin-api';
import { Config } from '@backstage/config';
import { LoggerService } from '@backstage/backend-plugin-api';
import {
  createChoreoApiService,
  ChoreoApiService,
} from '@internal/plugin-choreo-integration-backend/src/services/ChoreoApiService';
import type {
  ChoreoOrg,
  ChoreoProject,
  ChoreoComponent,
} from '@internal/plugin-choreo-integration-backend/src/services/ChoreoApiService/types';

/**
 * Provides entities from Choreo API
 */
export class ChoreoEntityProvider implements EntityProvider {
  private readonly taskRunner: SchedulerServiceTaskRunner;
  private connection?: EntityProviderConnection;
  private readonly logger: LoggerService;
  private readonly choreoApiService: Promise<ChoreoApiService>;

  constructor(
    taskRunner: SchedulerServiceTaskRunner,
    logger: LoggerService,
    config: Config,
  ) {
    this.taskRunner = taskRunner;
    this.logger = logger;
    this.choreoApiService = createChoreoApiService({ logger, config });
  }

  getProviderName(): string {
    return 'ChoreoEntityProvider';
  }

  async connect(connection: EntityProviderConnection): Promise<void> {
    this.connection = connection;
    await this.taskRunner.run({
      id: this.getProviderName(),
      fn: async () => {
        await this.run();
      },
    });
  }

  async run(): Promise<void> {
    if (!this.connection) {
      throw new Error('Connection not initialized');
    }

    try {
      this.logger.info('Fetching organizations and projects from Choreo API');

      const apiService = await this.choreoApiService;
      
      // Create dummy credentials for service-to-service calls
      // In a real implementation, you might want to use service credentials
      const serviceCredentials = {
        $$type: '@backstage/BackstageCredentials' as const,
        principal: {
          type: 'service' as const,
          subject: 'catalog-backend-module-choreo',
        },
      };

      // First, get all organizations
      const organizations = await apiService.getOrganizations({
        credentials: serviceCredentials,
      });
      this.logger.info(
        `Found ${organizations.length} organizations from Choreo`,
      );

      const allEntities: Entity[] = [];

      // Create Domain entities for each organization
      const domainEntities: Entity[] = organizations.map(org =>
        this.translateOrganizationToDomain(org),
      );
      allEntities.push(...domainEntities);

      // Get projects for each organization and create System entities
      for (const org of organizations) {
        try {
          const projects = await apiService.getProjects({
            credentials: serviceCredentials,
            orgId: parseInt(org.id, 10),
            orgHandler: org.handle,
          });
          this.logger.info(
            `Found ${projects.length} projects in organization: ${org.name}`,
          );

          const systemEntities: Entity[] = projects.map(project =>
            this.translateProjectToSystem(project, org.handle),
          );
          allEntities.push(...systemEntities);

          // Get components for each project and create Component entities
          for (const project of projects) {
            try {
              const components = await apiService.getComponents({
                credentials: serviceCredentials,
                orgHandler: org.handle,
                projectId: project.id,
              });
              this.logger.info(
                `Found ${components.length} components in project: ${project.name}`,
              );

              const componentEntities: Entity[] = components.map(component =>
                this.translateComponentToEntity(component, org.handle, project.name),
              );
              allEntities.push(...componentEntities);
            } catch (error) {
              this.logger.warn(
                `Failed to fetch components for project ${project.name} in organization ${org.name}: ${error}`,
              );
            }
          }
        } catch (error) {
          this.logger.warn(
            `Failed to fetch projects for organization ${org.name}: ${error}`,
          );
        }
      }

      await this.connection.applyMutation({
        type: 'full',
        entities: allEntities.map(entity => ({
          entity,
          locationKey: `provider:${this.getProviderName()}`,
        })),
      });

      const domainCount = allEntities.filter(e => e.kind === 'Domain').length;
      const systemCount = allEntities.filter(e => e.kind === 'System').length;
      const componentCount = allEntities.filter(e => e.kind === 'Component').length;
      
      this.logger.info(
        `Successfully processed ${allEntities.length} entities (${domainCount} domains, ${systemCount} systems, ${componentCount} components)`,
      );
    } catch (error) {
      this.logger.error(`Failed to run ChoreoEntityProvider: ${error}`);
    }
  }

  /**
   * Translates a ChoreoOrg from Choreo API to a Backstage Domain entity
   */
  private translateOrganizationToDomain(organization: ChoreoOrg): Entity {
    const domainEntity: Entity = {
      apiVersion: 'backstage.io/v1alpha1',
      kind: 'Domain',
      metadata: {
        name: organization.handle,
        title: organization.name,
        description: `Choreo organization: ${organization.name}`,
        tags: ['choreo', 'organization', 'domain'],
        annotations: {
          'backstage.io/managed-by-location': `provider:${this.getProviderName()}`,
          'backstage.io/managed-by-origin-location': `provider:${this.getProviderName()}`,
          'choreo.io/organization-id': organization.id,
          'choreo.io/organization-uuid': organization.uuid,
          'choreo.io/organization-handle': organization.handle,
          'choreo.io/organization-name': organization.name,
          'choreo.io/owner-id': organization.owner.id,
          'choreo.io/owner-idp-id': organization.owner.idpId,
          'choreo.io/created-at': organization.owner.createdAt,
        },
        labels: {
          'choreo.io/managed': 'true',
        },
      },
      spec: {
        owner: 'guests', // This could be configured or mapped from organization metadata
      },
    };

    return domainEntity;
  }

  /**
   * Translates a ChoreoProject from Choreo API to a Backstage System entity
   */
  private translateProjectToSystem(
    project: ChoreoProject,
    orgHandle: string,
  ): Entity {
    const systemEntity: Entity = {
      apiVersion: 'backstage.io/v1alpha1',
      kind: 'System',
      metadata: {
        name: `${orgHandle}-${project.handler}`,
        title: project.name,
        description: project.description || `Choreo project: ${project.name}`,
        tags: ['choreo', 'project', 'system'],
        annotations: {
          'backstage.io/managed-by-location': `provider:${this.getProviderName()}`,
          'backstage.io/managed-by-origin-location': `provider:${this.getProviderName()}`,
          'choreo.io/project-id': project.id,
          'choreo.io/project-name': project.name,
          'choreo.io/project-handler': project.handler,
          'choreo.io/project-version': project.version,
          'choreo.io/organization-id': project.orgId.toString(),
          'choreo.io/organization-handle': orgHandle,
          'choreo.io/region': project.region,
          'choreo.io/created-date': project.createdDate,
          'choreo.io/updated-at': project.updatedAt,
          ...(project.type && { 'choreo.io/project-type': project.type }),
          ...(project.gitProvider && { 'choreo.io/git-provider': project.gitProvider }),
          ...(project.gitOrganization && { 'choreo.io/git-organization': project.gitOrganization }),
          ...(project.repository && { 'choreo.io/repository': project.repository }),
          ...(project.branch && { 'choreo.io/branch': project.branch }),
        },
        labels: {
          'choreo.io/managed': 'true',
        },
      },
      spec: {
        owner: 'guests', // This could be mapped from project metadata
        domain: orgHandle, // Link to the parent domain (organization)
      },
    };

    return systemEntity;
  }

  /**
   * Translates a ChoreoComponent from Choreo API to a Backstage Component entity
   */
  private translateComponentToEntity(
    component: ChoreoComponent,
    orgHandle: string,
    projectName: string,
  ): Entity {
    // Map Choreo component types to Backstage component types
    let backstageComponentType: string = component.displayType.toLowerCase();

    // Map specific Choreo types to Backstage types
    switch (component.displayType) {
      case 'Service':
        backstageComponentType = 'service';
        break;
      case 'Web Application':
        backstageComponentType = 'website';
        break;
      case 'Scheduled Task':
        backstageComponentType = 'service';
        break;
      case 'Manual Task':
        backstageComponentType = 'service';
        break;
      default:
        backstageComponentType = 'service'; // Default fallback
    }

    // Map Choreo status to Backstage lifecycle
    let lifecycle: string = 'experimental';
    switch (component.status.toLowerCase()) {
      case 'active':
      case 'running':
        lifecycle = 'production';
        break;
      case 'inactive':
      case 'stopped':
        lifecycle = 'deprecated';
        break;
      case 'development':
      case 'building':
        lifecycle = 'experimental';
        break;
      default:
        lifecycle = 'experimental';
    }

    const componentEntity: Entity = {
      apiVersion: 'backstage.io/v1alpha1',
      kind: 'Component',
      metadata: {
        name: `${orgHandle}-${projectName}-${component.handler}`,
        title: component.displayName || component.name,
        description: component.description || `Choreo component: ${component.name}`,
        tags: ['choreo', 'component', component.displayType.toLowerCase().replace(/\s+/g, '-')],
        annotations: {
          'backstage.io/managed-by-location': `provider:${this.getProviderName()}`,
          'backstage.io/managed-by-origin-location': `provider:${this.getProviderName()}`,
          'choreo.io/component-id': component.id,
          'choreo.io/component-name': component.name,
          'choreo.io/component-handler': component.handler,
          'choreo.io/component-display-name': component.displayName,
          'choreo.io/component-display-type': component.displayType,
          'choreo.io/component-sub-type': component.componentSubType,
          'choreo.io/component-version': component.version,
          'choreo.io/project-id': component.projectId,
          'choreo.io/project-name': projectName,
          'choreo.io/organization-handle': orgHandle,
          'choreo.io/status': component.status,
          'choreo.io/init-status': component.initStatus,
          'choreo.io/created-at': component.createdAt,
          'choreo.io/is-system-component': component.isSystemComponent?.toString() || 'false',
          ...(component.lastBuildDate && { 'choreo.io/last-build-date': component.lastBuildDate }),
          ...(component.repository?.buildpackConfig && {
            'choreo.io/buildpack-language': component.repository.buildpackConfig.buildpack?.language || '',
            'choreo.io/language-version': component.repository.buildpackConfig.languageVersion || '',
          }),
        },
        labels: {
          'choreo.io/managed': 'true',
        },
      },
      spec: {
        type: backstageComponentType,
        lifecycle: lifecycle,
        owner: 'guests', // This could be mapped from component metadata
        system: `${orgHandle}-${projectName}`, // Link to the parent system (project)
      },
    };

    return componentEntity;
  }
}
