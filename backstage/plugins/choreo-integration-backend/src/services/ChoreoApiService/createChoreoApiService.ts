import { LoggerService } from '@backstage/backend-plugin-api';
import { Config } from '@backstage/config';
import { NotFoundError, ServiceUnavailableError } from '@backstage/errors';
import { ChoreoApiService, ChoreoOrg, ChoreoProject, ProjectCreationEligibility, ChoreoComponent, ChoreoEnvironment } from './types';

export async function createChoreoApiService({
  logger,
  config,
}: {
  logger: LoggerService;
  config: Config;
}): Promise<ChoreoApiService> {
  logger.info('Initializing ChoreoApiService');

  // Get configuration values
  const apiUrl = config.getString('choreo.apiUrl');
  const projectApiUrl = config.getString('choreo.projectApiUrl');
  const bearerToken = config.getString('choreo.bearerToken');

  if (!apiUrl || !projectApiUrl || !bearerToken) {
    throw new Error(
      'Choreo API configuration is missing. Please ensure choreo.apiUrl, choreo.projectApiUrl and choreo.bearerToken are configured.',
    );
  }

  return {
    async getOrganizations(_options) {
      logger.info('Fetching organizations from Choreo API');

      try {
        const response = await fetch(`${apiUrl}/orgs`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const data: ChoreoOrg[] = await response.json();

        logger.info('Successfully fetched organizations from Choreo API', {
          count: data.length,
        });

        return data;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch organizations from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getProjects(options) {
      logger.info('Fetching projects from Choreo API', {
        orgId: options.orgId,
        orgHandler: options.orgHandler,
      });

      try {
        const graphqlQuery = {
          query: `query{projects(orgId: ${options.orgId}){
            id, orgId, name, version, createdDate, handler, region, description,
            defaultDeploymentPipelineId,deploymentPipelineIds,
            type, gitProvider, gitOrganization, repository, branch,
            secretRef, updatedAt
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const projects: ChoreoProject[] = result?.data?.projects || [];

        logger.info('Successfully fetched projects from Choreo API', {
          count: projects.length,
        });

        return projects;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch projects from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getProjectCreationEligibility(options) {
      logger.info('Checking project creation eligibility from Choreo API', {
        orgId: options.orgId,
        orgHandler: options.orgHandler,
      });

      try {
        const graphqlQuery = {
          query: `query{projectCreationEligibility(orgId: ${options.orgId}, orgHandler: "${options.orgHandler}"){
            isProjectCreationAllowed
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const eligibility: ProjectCreationEligibility = result?.data?.projectCreationEligibility || { isProjectCreationAllowed: false };

        logger.info('Successfully checked project creation eligibility from Choreo API', {
          isAllowed: eligibility.isProjectCreationAllowed,
        });

        return eligibility;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to check project creation eligibility from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getComponents(options) {
      logger.info('Fetching components from Choreo API', {
        orgHandler: options.orgHandler,
        projectId: options.projectId,
      });

      try {
        const graphqlQuery = {
          query: `query{components(orgHandler: "${options.orgHandler}", projectId: "${options.projectId}"){
            projectId, id, description, status, initStatus, name, handler, displayName,
            displayType, version, createdAt, lastBuildDate, orgHandler, isSystemComponent,
            repository {
              buildpackConfig { versionId, buildContext, languageVersion, buildpack { id, language } },
              byocWebAppBuildConfig { id, dockerContext, webAppType }
            },
            componentSubType,
            apiVersions { apiVersion, proxyName, proxyUrl, proxyId, id, state, latest, branch, accessibility },
            deploymentTracks { id, createdAt, updatedAt, apiVersion, branch, description, componentId, latest, versionStrategy, autoDeployEnabled }
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const components: ChoreoComponent[] = result?.data?.components || [];

        logger.info('Successfully fetched components from Choreo API', {
          count: components.length,
        });

        // Log each component for debugging
        components.forEach((component, index) => {
          logger.info(`Component ${index + 1}: ${component.handler} (${component.name}) - Status: ${component.status}, InitStatus: ${component.initStatus}`);
        });

        return components;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch components from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },

    async getEnvironments(options) {
      logger.info('Fetching environments from Choreo API', {
        orgUuid: options.orgUuid,
        projectId: options.projectId,
        type: options.type || 'external',
      });

      try {
        const graphqlQuery = {
          query: `query{environments(orgUuid: "${options.orgUuid}", type: "${options.type || 'external'}", projectId: "${options.projectId}"){
            name, id, choreoEnv, vhost, apiEnvName, isMigrating, apimEnvId, namespace,
            sandboxVhost, critical, isPdp, promoteFrom, dpId, templateId, scaleToZeroEnabled
          }}`,
        };

        const response = await fetch(`${projectApiUrl}/graphql`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${bearerToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(graphqlQuery),
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new NotFoundError('Choreo GraphQL API endpoint not found');
          }
          if (response.status >= 500) {
            throw new ServiceUnavailableError(
              `Choreo API service unavailable: ${response.statusText}`,
            );
          }
          throw new Error(
            `Choreo API request failed: ${response.status} ${response.statusText}`,
          );
        }

        const result = await response.json();
        const environments: ChoreoEnvironment[] = result?.data?.environments || [];

        logger.info('Successfully fetched environments from Choreo API', {
          count: environments.length,
        });

        return environments;
      } catch (error) {
        if (error instanceof NotFoundError || error instanceof ServiceUnavailableError) {
          throw error;
        }

        logger.error('Failed to fetch environments from Choreo API', error instanceof Error ? error : new Error(String(error)));
        throw new ServiceUnavailableError(
          'Unable to connect to Choreo API. Please try again later.',
        );
      }
    },
  };
}
